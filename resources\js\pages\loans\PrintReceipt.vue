<script setup lang="ts">
import { onMounted } from 'vue';

defineProps<{
    paymentRecord: any;
}>();

onMounted(() => {
    setTimeout(() => {
        window.print();
    }, 100);
});

const formatAddress = (address: any | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;

    const state = address.state_selection?.value;
    const country = address.country_selection?.value;

    const line3Parts = [address.postcode, address.city, state, country].filter(Boolean).join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

const formatCurrency = (value: number | null | undefined) => {
    if (value == null) return '-';
    return new Intl.NumberFormat('en-MY', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(value);
};
</script>

<template>
    <div class="mx-auto mt-12 w-[90%] text-[18px]">
        <!-- Header -->
        <div class="flex justify-between text-[#136bd0]">
            <div v-if="paymentRecord.company_detail.company_logo" class="card rounded-sm p-1">
                <img :src="paymentRecord.company_detail.company_logo" alt="Logo preview" class="h-[74px] w-[74px] rounded object-contain" />
            </div>
            <div class="w-2/3 pl-1">
                <h1 class="m-0 text-[18px] font-bold">{{ paymentRecord.company_detail.company }}</h1>
                <p class="text-[12px]">
                    {{ paymentRecord.company_detail.business_registration_no }}
                    {{
                        paymentRecord.company_detail.old_business_registration_no
                            ? `(${paymentRecord.company_detail.old_business_registration_no})`
                            : ''
                    }}
                    <template v-if="paymentRecord.team_detail.address">
                        <br />
                        {{ formatAddress(paymentRecord.team_detail.address) }}
                    </template>
                </p>
            </div>
            <div class="w-1/3 space-y-1 text-[12px]">
                <p>{{ paymentRecord.team_detail.contact }}</p>
                <p>{{ paymentRecord.team_detail.email }}</p>
                <p>{{ paymentRecord.team_detail.website }}</p>
            </div>
        </div>

        <!-- Receipt Info -->
        <div class="mt-10 flex justify-between text-sm text-[12px]">
            <div class="w-1/2">
                <p><strong>Receipt No.:</strong> {{ paymentRecord.code }}</p>
                <p>To:</p>
                <div v-for="(customer, index) in paymentRecord.customer" :key="index">
                    <p class="pb-2 text-[12px]">
                        {{ customer.name }} <br />
                        {{ customer.identity_no }}
                    </p>
                </div>
                <p>M/S: {{ paymentRecord.agent_code }} - {{ paymentRecord.agent }}</p>
            </div>
            <div class="w-1/2">
                <p><strong>Office Receipt:</strong></p>
                <p>Repayment Date: {{ paymentRecord.repayment_date }}</p>
                <p>Loan No: {{ paymentRecord.loan_no }}</p>
                <p>Payment No.:<br />{{ paymentRecord.payment_ref_code }}</p>
            </div>
        </div>

        <!-- Payment Details Table -->
        <div class="mt-6 text-[12px]">
            <table class="w-full">
                <thead>
                    <tr class="border-y border-black">
                        <th class="py-1 text-left">Repayment Type</th>
                        <th class="py-1 text-right">Amount (RM)</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="pt-5">Instalment Total:</td>
                        <td class="pt-5 text-right">{{ paymentRecord.amount }}</td>
                    </tr>
                    <tr class="border-b border-black text-gray-500">
                        <td>&nbsp;</td>
                        <td class="text-right"></td>
                    </tr>
                    <tr class="pt-5">
                        <td></td>
                        <td class="text-right">
                            Total Paid: <strong>{{ paymentRecord.amount }}</strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="pt-5">
                            PAY TYPE:
                            {{ paymentRecord.payment_method }} ({{ paymentRecord.created_at }})
                        </td>
                        <td></td>
                    </tr>
                    <tr class="h-2">
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>
                            Remark:<br />
                            {{ paymentRecord.remark }}
                        </td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Footer -->
        <div class="mt-[150px] text-[12px]">
            <div class="text-right">
                <div class="inline-block w-[200px] border-t border-black pt-1">Issued By</div>
            </div>
        </div>
    </div>
</template>
