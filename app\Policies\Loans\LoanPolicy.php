<?php

namespace App\Policies\Loans;

use App\Enums\AccessControl\PermissionName;
use App\Models\Company;
use App\Models\Loan;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;

class LoanPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any loans.
     *
     * @return bool
     */
    public function viewAny(User $user)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOANS->value);
    }

    /**
     * Determine whether the user can view the loan.
     *
     * @return bool
     */
    public function view(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::READ_LOANS->value);
    }

    /**
     * Determine whether the user can create loans.
     *
     * @return bool
     */
    public function create(User $user)
    {
        return $user->hasPermissionTo(PermissionName::CREATE_LOANS->value);
    }

    /**
     * Determine whether the user can update the loan.
     *
     * @return bool
     */
    public function update(User $user, Loan $loan)
    {
        $loanCompany = Company::find($loan->company_id);

        $loanStatus = $loan->status;

        $statusPermission = match ($loanStatus->value) {
            1 => PermissionName::PENDING_PROCESS_UPDATE_LOAN_STATUSES->value,
            2 => PermissionName::PENDING_REVIEW_UPDATE_LOAN_STATUSES->value,
            3 => PermissionName::PENDING_APPROVAL_UPDATE_LOAN_STATUSES->value,
            4 => PermissionName::REJECTED_UPDATE_LOAN_STATUSES->value,
            5 => PermissionName::APPROVED_UPDATE_LOAN_STATUSES->value,
            6 => PermissionName::CUSTOMER_REJECTED_UPDATE_LOAN_STATUSES->value,
            7 => PermissionName::CUSTOMER_APPROVED_UPDATE_LOAN_STATUSES->value,
            8 => PermissionName::ON_GOING_UPDATE_LOAN_STATUSES->value,
            9 => PermissionName::ON_GOING_OVERDUE_UPDATE_LOAN_STATUSES->value,
            10 => PermissionName::COMPLETED_UPDATE_LOAN_STATUSES->value,
            11 => PermissionName::CANCELLED_UPDATE_LOAN_STATUSES->value,
            default => null,
        };

        if ($user->isHeadquarterAdmin() && $user->hasHeadquarterAccess($loanCompany->headquarter_id) && $loanCompany->is_headquarter) {
            return $user->hasPermissionTo(PermissionName::UPDATE_LOANS->value)
                && ($statusPermission ? $user->hasPermissionTo($statusPermission) : false);
        }

        if (! $user->hasCompanyAccess($loan->company_id)) {
            return false;
        }

        if ($user->isTeamAdminOrUser() && ! $user->hasTeamAccess($loan->team_id)) {
            return false;
        }

        return $user->hasPermissionTo(PermissionName::UPDATE_LOANS->value)
            && ($statusPermission ? $user->hasPermissionTo($statusPermission) : false);
    }

    /**
     * Determine whether the user can delete the loan.
     *
     * @return bool
     */
    public function delete(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }

    /**
     * Determine whether the user can restore the loan.
     *
     * @return bool
     */
    public function restore(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }

    /**
     * Determine whether the user can permanently delete the loan.
     *
     * @return bool
     */
    public function forceDelete(User $user, Loan $loan)
    {
        return $user->hasPermissionTo(PermissionName::DELETE_LOANS->value);
    }
}
