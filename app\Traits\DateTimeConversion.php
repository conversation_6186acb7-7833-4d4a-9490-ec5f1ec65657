<?php

namespace App\Traits;

use Carbon\Carbon;

trait DateTimeConversion
{
    private function parseDateTime(?string $datetime): ?Carbon
    {
        return $datetime ? Carbon::parse($datetime) : null;
    }

    public function getCurrentDateTime(string $timezone = 'UTC'): Carbon
    {
        return Carbon::now()->setTimezone($timezone);
    }

    public function convertToTimezone(string $datetime, string $timezone = 'Asia/Singapore'): Carbon
    {
        return Carbon::parse($datetime, 'UTC')->setTimezone($timezone);
    }

    public function convertFromTimezoneToUTC(string $datetime, string $timezone = 'Asia/Singapore'): Carbon
    {
        return Carbon::parse($datetime, $timezone)->setTimezone('UTC');
    }

    public function formatDateTime(string|Carbon $datetime, string $format = 'Y-m-d H:i:s'): string
    {
        if ($datetime instanceof Carbon) {
            return $datetime->format($format);
        }

        return Carbon::parse($datetime)->format($format);
    }

    public function getDateRange(string $date, string $timezone = 'Asia/Singapore'): array
    {
        $fromDateLocal = "$date 00:00:00";
        $toDateLocal = "$date 23:59:59";

        $fromDateUTC = $this->convertFromTimezoneToUTC($fromDateLocal, $timezone);
        $toDateUTC = $this->convertFromTimezoneToUTC($toDateLocal, $timezone);

        return [$fromDateUTC, $toDateUTC];
    }

    public function getDateSOD(string $date, string $timezone = 'Asia/Singapore'): Carbon
    {
        $localEndOfDay = Carbon::parse($date, $timezone)->startOfDay();

        return $localEndOfDay->setTimezone('UTC');
    }

    public function getDateEOD(string $date, string $timezone = 'Asia/Singapore'): Carbon
    {
        $localEndOfDay = Carbon::parse($date, $timezone)->endOfDay();

        return $localEndOfDay->setTimezone('UTC');
    }
}
