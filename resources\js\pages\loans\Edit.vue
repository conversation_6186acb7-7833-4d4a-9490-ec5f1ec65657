<script setup lang="ts">
import Calendar from '@/components/calendar/Calendar.vue';
import Collateral from '@/components/customer/Collateral.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormField from '@/components/form/FormField.vue';
import FormInputCurrency from '@/components/form/FormInputCurrency.vue';
import FormInputSearch from '@/components/form/FormInputSearch.vue';
import FormShow from '@/components/form/FormShow.vue';
import Heading from '@/components/Heading.vue';
import RequiredIndicator from '@/components/RequiredIndicator.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { Selection } from '@/types';

const { formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

const handleCollateralResults = (collateral: number) => {
    fetchCustomerCollaterals(collateral);
};

type Address = {
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    selection_state_id: number | null;
    selection_country_id: number | null;
};

interface Props {
    loan: any;
    documentTypes: Selection[];
    loanTypes: Selection[];
    collateralTypes: Selection[];
    loanModes: Selection[];
    repaymentMethods: Selection[];
    genderTypes: Selection[];
    relationships: Selection[];
    telephoneCountries: Selection[];
    mobileCountries: Selection[];
    nationalities: Selection[];
    states: Selection[];
    countries: Selection[];
    employmentTerms: Selection[];
    businessClassifications: Selection[];
    occupations: Selection[];
    bankAccountTypes: Selection[];
    bankTypes: Selection[];
}

const props = defineProps<Props>();
const statusLabel = (status: number) => {
    const labels = [
        'Pending Process', // 1
        'Pending Review', // 2
        'Pending Approval', // 3
        'Rejected', // 4
        'Approved', // 5
        'Customer Rejected', // 6
        'Customer Accepted', // 7
        'On-going', // 8
        'On-going (Overdue)', // 9
        'Completed', // 10
        'Cancelled', // 11
    ];
    return labels[status - 1] ?? 'Unknown';
};
const activeTab = ref('personal');
const activeDocTab = ref('all');
const ordinal = (n: number) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const COLLATERAL_TYPE_ID = props.loanTypes.find((type) => type.value === 'Collateral')?.id ?? null;
const openAccordionIndex = ref<string | undefined>(undefined);
const loanStatus = {
    PENDING_PROCESS: 1,
    PENDING_REVIEW: 2,
    PENDING_APPROVAL: 3,
    REJECTED: 4,
    APPROVED: 5,
    CUSTOMER_REJECTED: 6,
    CUSTOMER_ACCEPTED: 7,
    ON_GOING: 8,
    ON_GOING_OVERDUE: 9,
    COMPLETED: 10,
    CANCELLED: 11,
};

const form = useForm({
    selection_type_id: props.loan.selection_type_id,
    agent_id: props.loan.agent_id,
    company_id: props.loan.company_id,
    borrower_id: props.loan.customer[0].id,
    team_id: props.loan.team_id,
    status: props.loan.status,
    _saveAsDraft: null as boolean | null,
    reject_reason: null as string | null,
    activeTab: null as string | null,

    collateral: props.loan.loan_customer_collaterals.map((collateral: any) => ({
        id: collateral.id,
        collateral_id: null,
        _delete: false,
    })),

    loan: {
        selection_mode_type_id: props.loan.loanDetail.selection_mode_type_id,
        loan_principle_amount: props.loan.loanDetail.loan_principle_amount,
        no_of_instalment: props.loan.loanDetail.no_of_instalment,
        instalment_amount: props.loan.loanDetail.instalment_amount,
        selection_repayment_method_id: props.loan.loanDetail.selection_repayment_method_id,
        late_payment_charges: 8.0,
        interest: props.loan.loanDetail.interest,
        selection_interest_period: props.loan.loanDetail.selection_mode_type_id,
        last_payment: props.loan.loanDetail.last_payment,
        commencement_date: props.loan.loanDetail.commencement_date,
        stamping_date: props.loan.loanDetail.stamping_date,
        next_due_date: props.loan.loanDetail.next_due_date,
        rebate: props.loan.loanDetail.rebate,
        stamp_duty: props.loan.loanDetail.stamp_duty,
        attestation_fee: props.loan.loanDetail.attestation_fee,
        legal_fee: props.loan.loanDetail.legal_fee,
        processing_fee: props.loan.loanDetail.processing_fee,
        misc_charges: props.loan.loanDetail.misc_charges,
        loan_disbursement_amount: props.loan.loanDetail.loan_disbursement_amount,
    },

    emergency: {
        id: props.loan.emergency.id,
        personal: {
            name: props.loan.emergency.name,
            identity_no: props.loan.emergency.identity_no,
            birth_date: props.loan.emergency.birth_date,
            age: props.loan.emergency.age,
            selection_gender_id: props.loan.emergency.selection_gender_id,
            selection_relationship_id: props.loan.emergency.selection_relationship_id,
            selection_nationality_id: props.loan.emergency.selection_nationality_id,
            contact: {
                telephone: props.loan.emergency.personal_contact.telephone,
                selection_telephone_country_id: props.loan.emergency.personal_contact.selection_telephone_country_id,
                telephone_id: props.loan.emergency.personal_contact.telephone_id,
                mobile_phone: props.loan.emergency.personal_contact.mobile_phone,
                selection_mobile_country_id: props.loan.emergency.personal_contact.selection_mobile_country_id,
                mobile_id: props.loan.emergency.personal_contact.mobile_id,
            },
            address: props.loan.emergency.personal_address
                ? {
                      id: props.loan.emergency.personal_address.id,
                      line_1: props.loan.emergency.personal_address.line_1,
                      line_2: props.loan.emergency.personal_address.line_2,
                      postcode: props.loan.emergency.personal_address.postcode,
                      city: props.loan.emergency.personal_address.city,
                      selection_state_id: props.loan.emergency.personal_address.selection_state_id,
                      state: props.loan.emergency.personal_address.state,
                      state_selection: props.loan.emergency.personal_address.state_selection,
                      selection_country_id: props.loan.emergency.personal_address.selection_country_id,
                      country: props.loan.emergency.personal_address.country,
                      country_selection: props.loan.emergency.personal_address.country_selection,
                  }
                : null,
        },
        employment: {
            employment_name: props.loan.emergency.employment_name,
            contact: {
                telephone: props.loan.emergency.employment_contact.telephone,
                selection_telephone_country_id: props.loan.emergency.employment_contact.selection_telephone_country_id,
                telephone_id: props.loan.emergency.employment_contact.telephone_id,
                mobile_phone: props.loan.emergency.employment_contact.mobile_phone,
                selection_mobile_country_id: props.loan.emergency.employment_contact.selection_mobile_country_id,
                mobile_id: props.loan.emergency.employment_contact.mobile_id,
            },
            address: props.loan.emergency.employment_address
                ? {
                      id: props.loan.emergency.employment_address.id,
                      line_1: props.loan.emergency.employment_address.line_1,
                      line_2: props.loan.emergency.employment_address.line_2,
                      postcode: props.loan.emergency.employment_address.postcode,
                      city: props.loan.emergency.employment_address.city,
                      selection_state_id: props.loan.emergency.employment_address.selection_state_id,
                      state: props.loan.emergency.employment_address.state,
                      state_selection: props.loan.emergency.employment_address.state_selection,
                      selection_country_id: props.loan.emergency.employment_address.selection_country_id,
                      country: props.loan.emergency.employment_address.country,
                      country_selection: props.loan.emergency.employment_address.country_selection,
                  }
                : null,
        },
    },

    guarantors: props.loan.guarantors.map((guarantor: any) => ({
        id: guarantor.id,
        personal: {
            name: guarantor.name,
            identity_no: guarantor.identity_no,
            birth_date: guarantor.birth_date,
            age: guarantor.age,
            selection_gender_id: guarantor.selection_gender_id,
            selection_relationship_id: guarantor.selection_relationship_id,
            selection_nationality_id: guarantor.selection_nationality_id,
            contact: {
                telephone: guarantor.personal_contact.telephone,
                selection_telephone_country_id: guarantor.personal_contact.selection_telephone_country_id,
                telephone_id: guarantor.personal_contact.telephone_id,
                mobile_phone: guarantor.personal_contact.mobile_phone,
                selection_mobile_country_id: guarantor.personal_contact.selection_mobile_country_id,
                mobile_id: guarantor.personal_contact.mobile_id,
            },
            address: guarantor.personal_address
                ? {
                      id: guarantor.personal_address.id,
                      line_1: guarantor.personal_address.line_1,
                      line_2: guarantor.personal_address.line_2,
                      postcode: guarantor.personal_address.postcode,
                      city: guarantor.personal_address.city,
                      selection_state_id: guarantor.personal_address.selection_state_id,
                      state: guarantor.personal_address.state,
                      state_selection: guarantor.personal_address.state_selection,
                      selection_country_id: guarantor.personal_address.selection_country_id,
                      country: guarantor.personal_address.country,
                      country_selection: guarantor.personal_address.country_selection,
                  }
                : null,
        },
        employment: {
            employment_name: guarantor.employment_name,
            length_service_year: guarantor.length_service_year,
            length_service_month: guarantor.length_service_month,
            job_position: guarantor.job_position,
            selection_terms_of_employment_id: guarantor.selection_terms_of_employment_id,
            selection_occupation_id: guarantor.selection_occupation_id,
            selection_business_classification_id: guarantor.selection_business_classification_id,
            contact: {
                telephone: guarantor.employment_contact.telephone,
                selection_telephone_country_id: guarantor.employment_contact.selection_telephone_country_id,
                telephone_id: guarantor.employment_contact.telephone_id,
                mobile_phone: guarantor.employment_contact.mobile_phone,
                selection_mobile_country_id: guarantor.employment_contact.selection_mobile_country_id,
                mobile_id: guarantor.employment_contact.mobile_id,
            },
            address: guarantor.employment_address
                ? {
                      id: guarantor.employment_address.id,
                      line_1: guarantor.employment_address.line_1,
                      line_2: guarantor.employment_address.line_2,
                      postcode: guarantor.employment_address.postcode,
                      city: guarantor.employment_address.city,
                      selection_state_id: guarantor.employment_address.selection_state_id,
                      state: guarantor.employment_address.state,
                      state_selection: guarantor.employment_address.state_selection,
                      selection_country_id: guarantor.employment_address.selection_country_id,
                      country: guarantor.employment_address.country,
                      country_selection: guarantor.employment_address.country_selection,
                  }
                : null,
        },
        _delete: false,
    })),

    bank: props.loan.bankDetails.map((bank: any) => ({
        id: bank.id,
        selection_type_id: bank.selection_type_id,
        selection_bank_id: bank.selection_bank_id,
        account_name: bank.account_name,
        account_no: bank.account_no,
        save_bank: true,
        _delete: false,
    })),

    document: props.loan.documents.map((doc: any) => ({
        id: doc.id,
        _delete: false,
    })),
});

const personalFields = computed(() => [
    {
        id: 'company_id',
        label: 'Company Name',
        type: 'show',
        modelValue: props.loan.company,
    },
    {
        id: 'team_id',
        label: 'Team Name',
        type: 'show',
        modelValue: props.loan.team,
    },
    {
        id: 'agent_id',
        label: 'Agent',
        type: 'show',
        modelValue: props.loan.agent,
    },
    {
        id: 'loan_type',
        label: 'Loan Type',
        type: 'select',
        required: true,
        placeholder: 'Loan Type',
        error: form.errors.selection_type_id,
        options: formatSelectionOptions(props.loanTypes),
        modelValue: form.selection_type_id,
        updateValue: (value: number) => (form.selection_type_id = value),
    },
]);

const loanFields = computed(() => [
    {
        id: 'loan_mode',
        label: 'Loan Mode',
        type: 'select',
        required: true,
        placeholder: 'Loan Mode',
        error: form.errors['loan.selection_mode_type_id'],
        options: formatSelectionOptions(props.loanModes),
        modelValue: form.loan.selection_mode_type_id,
        updateValue: (value: number) => (form.loan.selection_mode_type_id = value),
    },
    {
        id: 'loan_principle_of_amount',
        label: 'Loan Principle of Amount (RM)',
        type: 'currency',
        required: true,
        placeholder: 'Loan Principle of Amount',
        error: form.errors['loan.loan_principle_amount'],
        modelValue: form.loan.loan_principle_amount,
        updateValue: (value: string) => (form.loan.loan_principle_amount = value),
    },
    {
        id: 'no_of_instalment',
        label: 'No of Instalment',
        type: 'input',
        required: true,
        placeholder: 'No of Instalment',
        error: form.errors['loan.no_of_instalment'],
        modelValue: form.loan.no_of_instalment,
        updateValue: (value: string) => (form.loan.no_of_instalment = value),
    },
    {
        id: 'last_payment',
        label: 'Last Payment (RM)',
        type: 'currency',
        required: true,
        placeholder: 'Last Payment',
        error: form.errors['loan.last_payment'],
        modelValue: form.loan.last_payment,
        updateValue: (value: number) => (form.loan.last_payment = value),
    },
    {
        id: 'repayment_method',
        label: 'Repayment Method',
        type: 'select',
        required: true,
        placeholder: 'Repayment Method',
        error: form.errors['loan.selection_repayment_method_id'],
        options: formatSelectionOptions(props.repaymentMethods),
        modelValue: form.loan.selection_repayment_method_id,
        updateValue: (value: number) => (form.loan.selection_repayment_method_id = value),
    },
    {
        id: 'loan_instalment_amount',
        label: 'Instalment Amount (RM)',
        type: 'show',
        required: true,
        placeholder: 'Instalment Amount (RM)',
        error: form.errors['loan.instalment_amount'],
        modelValue: form.loan.instalment_amount,
        updateValue: (value: string) => (form.loan.instalment_amount = value),
    },
    {
        id: 'interest',
        label: 'Interest (%)',
        type: 'inputSelect',
        required: true,
        placeholder: 'Interest',
        error: form.errors['loan.interest'],
        selectPosition: 'right',
        modelValue: {
            select: form.loan.selection_mode_type_id,
            input: form.loan.interest,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.loan.interest = value.input;
            form.loan.selection_interest_period = value.select;
        },
        options: formatSelectionOptions(props.loanModes),
    },
    {
        id: 'late-payment-charges',
        label: 'Late Payment Charges (%)',
        type: 'show',
        required: true,
        placeholder: 'Late Payment Charges (%)',
        modelValue: form.loan.late_payment_charges,
        updateValue: (value: string) => (form.loan.late_payment_charges = parseFloat(value)),
    },
]);

const emergencyFields = computed(() => [
    {
        id: 'emergency_name',
        label: 'Name',
        type: 'input',
        required: false,
        placeholder: 'Name',
        error: form.errors['emergency.personal.name'],
        modelValue: form.emergency.personal.name,
        updateValue: (value: string) => (form.emergency.personal.name = value),
    },
    {
        id: 'emergency_identity_no',
        label: 'Identity No',
        type: 'input',
        required: false,
        placeholder: 'Identity No',
        error: form.errors['emergency.personal.identity_no'],
        modelValue: form.emergency.personal.identity_no,
        updateValue: (value: string) => (form.emergency.personal.identity_no = value),
    },
    {
        id: 'emergency_birth_date',
        label: 'Date of Birth',
        type: 'date',
        required: false,
        placeholder: 'Date of Birth',
        error: form.errors['emergency.personal.birth_date'],
        modelValue: form.emergency.personal.birth_date,
        updateValue: (value: string) => (form.emergency.personal.birth_date = value),
        disableFuture: true,
    },
    {
        id: 'emergency_age',
        label: 'Age',
        type: 'show',
        required: false,
        placeholder: 'Age',
        error: form.errors['emergency.personal.age'],
        modelValue: form.emergency.personal.age,
        updateValue: (value: number) => (form.emergency.personal.age = value),
    },
    {
        id: 'emergency_gender',
        label: 'Gender',
        type: 'radio',
        required: false,
        placeholder: 'Gender',
        options: formatSelectionOptions(props.genderTypes),
        error: form.errors['emergency.personal.selection_gender_id'],
        modelValue: form.emergency.personal.selection_gender_id,
        updateValue: (value: number) => (form.emergency.personal.selection_gender_id = value),
    },
    {
        id: 'relationship',
        label: 'Relationship',
        type: 'select',
        required: false,
        placeholder: 'Relationship',
        error: form.errors['emergency.personal.selection_relationship_id'],
        options: formatSelectionOptions(props.relationships),
        modelValue: form.emergency.personal.selection_relationship_id,
        updateValue: (value: number) => (form.emergency.personal.selection_relationship_id = value),
    },
    {
        id: 'nationality',
        label: 'Nationality',
        type: 'select',
        required: false,
        placeholder: 'Nationality',
        error: form.errors['emergency.personal.selection_nationality_id'],
        options: formatSelectionOptions(props.nationalities),
        modelValue: form.emergency.personal.selection_nationality_id,
        updateValue: (value: number) => (form.emergency.personal.selection_nationality_id = value),
    },
    {},
    {
        id: 'telephone',
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.personal.contact.telephone'],
        modelValue: {
            select: form.emergency.personal.contact.selection_telephone_country_id,
            input: form.emergency.personal.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.personal.contact.telephone = value.input;
            form.emergency.personal.contact.selection_telephone_country_id = value.select;
        },
        options: formatSelectionOptions(props.telephoneCountries),
    },
    {
        id: 'mobile',
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.personal.contact.mobile_phone'],
        modelValue: {
            select: form.emergency.personal.contact.selection_mobile_country_id,
            input: form.emergency.personal.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.personal.contact.mobile_phone = value.input;
            form.emergency.personal.contact.selection_mobile_country_id = value.select;
        },
        options: formatSelectionOptions(props.mobileCountries),
    },
    {
        id: 'emergency_line_1',
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: form.errors['emergency.personal.address.line_1'],
        modelValue: form.emergency.personal.address.line_1,
        updateValue: (value: string) => (form.emergency.personal.address.line_1 = value),
    },
    {
        id: 'line_2',
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: form.errors['emergency.personal.address.line_2'],
        modelValue: form.emergency.personal.address.line_2,
        updateValue: (value: string) => (form.emergency.personal.address.line_2 = value),
    },
    {
        id: 'postcode',
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: form.errors['emergency.personal.address.postcode'],
        modelValue: form.emergency.personal.address.postcode,
        updateValue: (value: string) => (form.emergency.personal.address.postcode = value),
    },
    {
        id: 'city',
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: form.errors['emergency.personal.address.city'],
        modelValue: form.emergency.personal.address.city,
        updateValue: (value: string) => (form.emergency.personal.address.city = value),
    },
    {
        id: 'state',
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        error: form.errors['emergency.personal.address.selection_state_id'],
        options: formatSelectionOptions(props.states),
        modelValue: form.emergency.personal.address.selection_state_id,
        updateValue: (value: number) => (form.emergency.personal.address.selection_state_id = value),
    },
    {
        id: 'country',
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        error: form.errors['emergency.personal.address.selection_country_id'],
        options: formatSelectionOptions(props.countries),
        modelValue: form.emergency.personal.address.selection_country_id,
        updateValue: (value: number) => (form.emergency.personal.address.selection_country_id = value),
    },
]);

const emergencyEmploymentFields = computed(() => [
    {
        id: 'emergency_employment_name',
        label: 'Name',
        type: 'input',
        required: false,
        placeholder: 'Name',
        error: form.errors['emergency.employment.employment_name'],
        modelValue: form.emergency.employment.employment_name,
        updateValue: (value: string) => (form.emergency.employment.employment_name = value),
    },
    {},
    {
        id: 'emergency_employment_telephone',
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.employment.contact.telephone'],
        modelValue: {
            select: form.emergency.employment.contact.selection_telephone_country_id,
            input: form.emergency.employment.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.employment.contact.telephone = value.input;
            form.emergency.employment.contact.selection_telephone_country_id = value.select;
        },
        options: formatSelectionOptions(props.telephoneCountries),
    },
    {
        id: 'emergency_employment_mobile',
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.employment.contact.mobile_phone'],
        modelValue: {
            select: form.emergency.employment.contact.selection_mobile_country_id,
            input: form.emergency.employment.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.employment.contact.mobile_phone = value.input;
            form.emergency.employment.contact.selection_mobile_country_id = value.select;
        },
        options: formatSelectionOptions(props.mobileCountries),
    },
    {
        id: 'emergency_employment_line_1',
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: form.errors['emergency.employment.address.line_1'],
        modelValue: form.emergency.employment.address.line_1,
        updateValue: (value: string) => (form.emergency.employment.address.line_1 = value),
    },
    {
        id: 'emergency_employment_line_2',
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: form.errors['emergency.employment.address.line_2'],
        modelValue: form.emergency.employment.address.line_2,
        updateValue: (value: string) => (form.emergency.employment.address.line_2 = value),
    },
    {
        id: 'emergency_employment_postcode',
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: form.errors['emergency.employment.address.postcode'],
        modelValue: form.emergency.employment.address.postcode,
        updateValue: (value: string) => (form.emergency.employment.address.postcode = value),
    },
    {
        id: 'emergency_employment_city',
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: form.errors['emergency.employment.address.city'],
        modelValue: form.emergency.employment.address.city,
        updateValue: (value: string) => (form.emergency.employment.address.city = value),
    },
    {
        id: 'emergency_employment_state',
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        error: form.errors['emergency.employment.address.selection_state_id'],
        options: formatSelectionOptions(props.states),
        modelValue: form.emergency.employment.address.selection_state_id,
        updateValue: (value: number) => (form.emergency.employment.address.selection_state_id = value),
    },
    {
        id: 'emergency_employment_country',
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        error: form.errors['emergency.employment.address.selection_country_id'],
        options: formatSelectionOptions(props.countries),
        modelValue: form.emergency.employment.address.selection_country_id,
        updateValue: (value: number) => (form.emergency.employment.address.selection_country_id = value),
    },
]);

const getGuarantorFields = (index: number) => [
    {
        id: `guarantor_name_${index}`,
        label: 'Name',
        type: 'input',
        required: false,
        placeholder: 'Name',
        error: (form.errors as any)?.[`guarantor.${index}.personal.name`],
        modelValue: form.guarantors[index].personal.name,
        updateValue: (value: string) => (form.guarantors[index].personal.name = value),
    },
    {
        id: `guarantor_identity_no_${index}`,
        label: 'Identity No',
        type: 'input',
        required: false,
        placeholder: 'Identity No',
        error: (form.errors as any)?.[`guarantor.${index}.personal.identity_no`],
        modelValue: form.guarantors[index].personal.identity_no,
        updateValue: (value: string) => (form.guarantors[index].personal.identity_no = value),
    },
    {
        id: `guarantor_birth_date_${index}`,
        label: 'Date of Birth',
        type: 'date',
        required: false,
        placeholder: 'Date of Birth',
        error: (form.errors as any)?.[`guarantor.${index}.personal.birth_date`],
        modelValue: form.guarantors[index].personal.birth_date,
        updateValue: (value: string) => (form.guarantors[index].personal.birth_date = value),
        disableFuture: true,
    },
    {
        id: `guarantor_age_${index}`,
        label: 'Age',
        type: 'show',
        required: false,
        placeholder: 'Age',
        error: (form.errors as any)?.[`guarantor.${index}.personal.age`],
        modelValue: form.guarantors[index].personal.age,
        updateValue: (value: number) => (form.guarantors[index].personal.age = value),
    },
    {
        id: `guarantor_gender_${index}`,
        label: 'Gender',
        type: 'radio',
        required: false,
        placeholder: 'Gender',
        options: formatSelectionOptions(props.genderTypes),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_gender_id`],
        modelValue: form.guarantors[index].personal.selection_gender_id,
        updateValue: (value: number) => (form.guarantors[index].personal.selection_gender_id = value),
    },
    {
        id: `guarantor_relationship_${index}`,
        label: 'Relationship',
        type: 'select',
        required: false,
        placeholder: 'Relationship',
        options: formatSelectionOptions(props.relationships),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_relationship_id`],
        modelValue: form.guarantors[index].personal.selection_relationship_id,
        updateValue: (value: number) => (form.guarantors[index].personal.selection_relationship_id = value),
    },
    {
        id: `guarantor_nationality_${index}`,
        label: 'Nationality',
        type: 'select',
        required: false,
        placeholder: 'Nationality',
        options: formatSelectionOptions(props.nationalities),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_nationality_id`],
        modelValue: form.guarantors[index].personal.selection_nationality_id,
        updateValue: (value: number) => (form.guarantors[index].personal.selection_nationality_id = value),
    },
    {},
    {
        id: `guarantor_telephone_${index}`,
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select Prefix',
        options: formatSelectionOptions(props.telephoneCountries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.contact.telephone`],
        modelValue: {
            select: form.guarantors[index].personal.contact.selection_telephone_country_id,
            input: form.guarantors[index].personal.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantors[index].personal.contact.telephone = value.input;
            form.guarantors[index].personal.contact.selection_telephone_country_id = value.select;
        },
    },
    {
        id: `guarantor_mobile_${index}`,
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select Prefix',
        options: formatSelectionOptions(props.mobileCountries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.contact.mobile_phone`],
        modelValue: {
            select: form.guarantors[index].personal.contact.selection_mobile_country_id,
            input: form.guarantors[index].personal.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantors[index].personal.contact.mobile_phone = value.input;
            form.guarantors[index].personal.contact.selection_mobile_country_id = value.select;
        },
    },
    {
        id: `guarantor_line_1_${index}`,
        label: 'Address Line 1',
        type: 'input',
        required: false,
        class: 'col-span-2',
        placeholder: 'Address Line 1',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.line_1`],
        modelValue: form.guarantors[index].personal.address.line_1,
        updateValue: (value: string) => (form.guarantors[index].personal.address.line_1 = value),
    },
    {
        id: `guarantor_line_2_${index}`,
        label: 'Address Line 2',
        type: 'input',
        required: false,
        class: 'col-span-2',
        placeholder: 'Address Line 2',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.line_2`],
        modelValue: form.guarantors[index].personal.address.line_2,
        updateValue: (value: string) => (form.guarantors[index].personal.address.line_2 = value),
    },
    {
        id: `guarantor_postcode_${index}`,
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.postcode`],
        modelValue: form.guarantors[index].personal.address.postcode,
        updateValue: (value: string) => (form.guarantors[index].personal.address.postcode = value),
    },
    {
        id: `guarantor_city_${index}`,
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.city`],
        modelValue: form.guarantors[index].personal.address.city,
        updateValue: (value: string) => (form.guarantors[index].personal.address.city = value),
    },
    {
        id: `guarantor_state_${index}`,
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        options: formatSelectionOptions(props.states),
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.selection_state_id`],
        modelValue: form.guarantors[index].personal.address.selection_state_id,
        updateValue: (value: number) => (form.guarantors[index].personal.address.selection_state_id = value),
    },
    {
        id: `guarantor_country_${index}`,
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        options: formatSelectionOptions(props.countries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.selection_country_id`],
        modelValue: form.guarantors[index].personal.address.selection_country_id,
        updateValue: (value: number) => (form.guarantors[index].personal.address.selection_country_id = value),
    },
];

const getGuarantorEmploymentFields = (index: number) => [
    {
        id: `guarantor_terms_of_employment_${index}`,
        label: 'Terms of Employment',
        type: 'select',
        required: false,
        placeholder: 'Terms of Employment',
        options: formatSelectionOptions(props.employmentTerms),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_terms_of_employment_id`],
        modelValue: form.guarantors[index].employment.selection_terms_of_employment_id,
        updateValue: (value: number) => (form.guarantors[index].employment.selection_terms_of_employment_id = value),
    },
    {
        id: `guarantor_length_service_year_${index}`,
        label: 'Length Service Year',
        type: 'input',
        required: false,
        placeholder: 'Length Service Year',
        error: (form.errors as any)?.[`guarantor.${index}.employment.length_service_year`],
        modelValue: form.guarantors[index].employment.length_service_year,
        updateValue: (value: string) => (form.guarantors[index].employment.length_service_year = value),
    },
    {
        id: `guarantor_employment_name_${index}`,
        label: 'Employment Name',
        type: 'input',
        required: false,
        placeholder: 'Employment Name',
        error: (form.errors as any)?.[`guarantor.${index}.employment.employment_name`],
        modelValue: form.guarantors[index].employment.employment_name,
        updateValue: (value: string) => (form.guarantors[index].employment.employment_name = value),
    },
    {
        id: `guarantor_length_service_month_${index}`,
        label: 'Length Service Month',
        type: 'input',
        required: false,
        placeholder: 'Length Service Month',
        error: (form.errors as any)?.['guarantor.0.employment.length_service_month'],
        modelValue: form.guarantors[index].employment.length_service_month,
        updateValue: (value: string) => (form.guarantors[index].employment.length_service_month = value),
    },
    {
        id: `guarantor_job_position_${index}`,
        label: 'Job Position',
        type: 'input',
        required: false,
        placeholder: 'Job Position',
        error: (form.errors as any)?.[`guarantor.${index}.employment.job_position`],
        modelValue: form.guarantors[index].employment.job_position,
        updateValue: (value: string) => (form.guarantors[index].employment.job_position = value),
    },
    {
        id: `guarantor_business_classification_${index}`,
        label: 'Business Classification',
        type: 'select',
        required: false,
        placeholder: 'Business Classification',
        options: formatSelectionOptions(props.businessClassifications),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_business_classification_id`],
        modelValue: form.guarantors[index].employment.selection_business_classification_id,
        updateValue: (value: number) => (form.guarantors[index].employment.selection_business_classification_id = value),
    },
    {
        id: `guarantor_occupation_${index}`,
        label: 'Occupation',
        type: 'select',
        required: false,
        placeholder: 'Occupation',
        options: formatSelectionOptions(props.occupations),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_occupation_id`],
        modelValue: form.guarantors[index].employment.selection_occupation_id,
        updateValue: (value: number) => (form.guarantors[index].employment.selection_occupation_id = value),
    },
    {},
    {
        id: `employment_telephone_${index}`,
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone',
        selectPlaceholder: 'Select Prefix',
        options: formatSelectionOptions(props.telephoneCountries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.contact.telephone`],
        modelValue: {
            select: form.guarantors[index].employment.contact.selection_telephone_country_id,
            input: form.guarantors[index].employment.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantors[index].employment.contact.telephone = value.input;
            form.guarantors[index].employment.contact.selection_telephone_country_id = value.select;
        },
    },
    {
        id: `employment_mobile_${index}`,
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select Prefix',
        options: formatSelectionOptions(props.mobileCountries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.contact.mobile_phone`],
        modelValue: {
            select: form.guarantors[index].employment.contact.selection_mobile_country_id,
            input: form.guarantors[index].employment.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantors[index].employment.contact.mobile_phone = value.input;
            form.guarantors[index].employment.contact.selection_mobile_country_id = value.select;
        },
    },
    {
        id: `employment_line_1_${index}`,
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.line_1`],
        modelValue: form.guarantors[index].employment.address.line_1,
        updateValue: (value: string) => (form.guarantors[index].employment.address.line_1 = value),
    },
    {
        id: `employment_line_2_${index}`,
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.line_2`],
        modelValue: form.guarantors[index].employment.address.line_2,
        updateValue: (value: string) => (form.guarantors[index].employment.address.line_2 = value),
    },
    {
        id: `employment_postcode_${index}`,
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.postcode`],
        modelValue: form.guarantors[index].employment.address.postcode,
        updateValue: (value: string) => (form.guarantors[index].employment.address.postcode = value),
    },
    {
        id: `employment_city_${index}`,
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.city`],
        modelValue: form.guarantors[index].employment.address.city,
        updateValue: (value: string) => (form.guarantors[index].employment.address.city = value),
    },
    {
        id: `employment_state_${index}`,
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        options: formatSelectionOptions(props.states),
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.selection_state_id`],
        modelValue: form.guarantors[index].employment.address.selection_state_id,
        updateValue: (value: number) => (form.guarantors[index].employment.address.selection_state_id = value),
    },
    {
        id: `employment_country_${index}`,
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        options: formatSelectionOptions(props.countries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.selection_country_id`],
        modelValue: form.guarantors[index].employment.address.selection_country_id,
        updateValue: (value: number) => (form.guarantors[index].employment.address.selection_country_id = value),
    },
];

const addGuarantor = () => {
    form.guarantors.push({
        personal: {
            name: null,
            identity_no: null,
            birth_date: null,
            age: null,
            selection_gender_id: null,
            selection_relationship_id: null,
            selection_nationality_id: null,
            contact: {
                selection_telephone_country_id: null,
                selection_mobile_country_id: null,
                telephone: null,
                mobile_phone: null,
            },
            address: {
                line_1: null,
                line_2: null,
                postcode: null,
                city: null,
                selection_state_id: null,
                selection_country_id: null,
            },
        },
        employment: {
            selection_terms_of_employment_id: null,
            length_service_year: null,
            employment_name: null,
            length_service_month: null,
            job_position: null,
            selection_business_classification_id: null,
            selection_occupation_id: null,
            contact: {
                selection_telephone_country_id: null,
                selection_mobile_country_id: null,
                telephone: null,
                mobile_phone: null,
            },
            address: {
                line_1: null,
                line_2: null,
                postcode: null,
                city: null,
                selection_state_id: null,
                selection_country_id: null,
            },
        },
        _delete: false,
    });
    openAccordionIndex.value = (form.guarantors.length - 1).toString();
};

const removeGuarantor = (index: number) => {
    if (form.guarantors[index].id) {
        form.guarantors[index]._delete = true;
    } else {
        form.guarantors.splice(index, 1);
    }
};

const addBank = () => {
    form.bank.push({
        selection_type_id: null,
        selection_bank_id: null,
        account_name: null,
        account_no: null,
        save_bank: true,
        _delete: false,
    });
};

const removeBank = (index: number) => {
    if (form.bank[index].id) {
        form.bank[index]._delete = true;
    } else {
        form.bank.splice(index, 1);
    }
};

const tabFlow = computed(() => ({
    personal: props.loan.type !== 'Collateral' ? 'loan' : 'loan',
    collateral: 'loan',
    loan: 'emergency',
    emergency: 'guarantor',
    guarantor: props.loan.status >= 5 ? 'bank' : 'document',
    bank: 'document',
    document: null,
}));

const tabBackFlow = computed(() => ({
    collateral: 'personal',
    loan: props.loan.type !== 'Collateral' ? 'personal' : 'collateral',
    emergency: 'loan',
    guarantor: 'emergency',
    document: props.loan.status >= 5 ? 'bank' : 'guarantor',
    bank: 'guarantor',
}));

const goToNextTab = () => {
    const current = activeTab.value as keyof typeof tabFlow.value;
    const next = tabFlow.value[current];
    if (next) activeTab.value = next;
};

const goToPreviousTab = () => {
    const current = activeTab.value as keyof typeof tabBackFlow.value;
    const prev = tabBackFlow.value[current];
    if (prev) activeTab.value = prev;
};

const tabItems = computed(() => {
    const items = [
        { label: 'Personal Details', value: 'personal' },
        { label: 'Collateral Details', value: 'collateral', disabled: form.selection_type_id !== COLLATERAL_TYPE_ID },
        { label: 'Loan Details', value: 'loan' },
        { label: 'Emergency Contact', value: 'emergency' },
        { label: 'Guarantor Contact', value: 'guarantor' },
        { label: 'Supporting Document', value: 'document' },
    ];

    if (props.loan.status >= 7) {
        const insertIndex = items.findIndex((item) => item.value === 'guarantor') + 1;
        items.splice(insertIndex, 0, { label: 'Bank Account Details', value: 'bank' });
    }

    return items;
});

const docItems = computed(() => [
    { label: 'All', value: 'all' },
    { label: 'Customer Documents', value: 'customer-doc' },
    { label: 'Collateral Documents', value: 'collateral-doc' },
    { label: 'Security Documents', value: 'security-doc' },
]);

type FileData = {
    id?: number;
    name: string;
    size: number;
    url: string;
    typeId: number;
    type: string;
    createdAt: string;
    uploadedBy: string;
};

type TabKey = 'customer-doc' | 'collateral-doc' | 'security-doc' | 'all';
const categorizedFiles = reactive<Record<TabKey, FileData[]>>({
    'customer-doc': [],
    'collateral-doc': [],
    'security-doc': [],
    all: [],
});

const selectedDocIds = ref<(string | number | undefined)[]>([]);

const allDocs = computed(() => {
    return Object.values(categorizedFiles).flat();
});

const selectedFile = computed(() => {
    const id = selectedDocIds.value[0];
    return allDocs.value.find((doc) => doc.id === id) || null;
});

const preselectedDocIds = new Set((props.loan.documents || []).map((doc: any) => doc.id));

const toggleSelect = (id: number | undefined) => {
    if (id == null) return;

    const index = selectedDocIds.value.indexOf(id);

    const isPreselected = preselectedDocIds.has(id);

    if (props.loan.status === 1) {
        if (index >= 0) {
            // Unselect
            selectedDocIds.value.splice(index, 1);

            if (isPreselected) {
                // Mark for deletion
                const existing = form.document.find((doc) => doc.id === id);
                if (existing) {
                    existing._delete = true;
                } else {
                    form.document.push({ id, _delete: true });
                }
            } else {
                // Remove new doc entirely
                form.document = form.document.filter((doc) => doc.doc_id !== id);
            }
        } else {
            // Select
            selectedDocIds.value.push(id);

            if (isPreselected) {
                // Restore if previously marked for deletion
                const existing = form.document.find((doc) => doc.id === id);
                if (existing) {
                    existing._delete = false;
                }
            } else {
                // Add new selected doc
                form.document.push({ doc_id: id, _delete: false });
            }
        }
    } else {
        // Single-select
        if (selectedDocIds.value[0] === id) {
            selectedDocIds.value = []; // Deselect
        } else {
            selectedDocIds.value = [id]; // Only allow one
        }
    }
};

const typeToTabKey: Record<number, TabKey> = {};
const CUSTOMER_DOC_ID = props.documentTypes.find((type) => type.value === 'Customer Documents')?.id ?? null;
const COLLATERAL_DOC_ID = props.documentTypes.find((type) => type.value === 'Collateral Documents')?.id ?? null;
const SECURITY_DOC_ID = props.documentTypes.find((type) => type.value === 'Security Documents')?.id ?? null;

if (CUSTOMER_DOC_ID) typeToTabKey[CUSTOMER_DOC_ID] = 'customer-doc';
if (COLLATERAL_DOC_ID) typeToTabKey[COLLATERAL_DOC_ID] = 'collateral-doc';
if (SECURITY_DOC_ID) typeToTabKey[SECURITY_DOC_ID] = 'security-doc';

onMounted(async () => {
    const loanDocs = props.loan.documents || [];

    // Normalize and track unique URLs for deduplication
    const existingDocUrls = new Set(
        loanDocs.map((doc: any) => {
            const rawPath = doc.file?.url || doc.url || '';
            return rawPath.startsWith('http') ? rawPath : `${window.location.origin}/storage/${rawPath}`;
        }),
    );

    const combinedDocuments = [...loanDocs];

    if (props.loan.status === 1 && Array.isArray(props.loan.customer)) {
        for (const customer of props.loan.customer) {
            try {
                const res = await axios.get(`/api/customers/${customer.customer_id}/documents`);
                const customerDocs = res.data;

                for (const doc of customerDocs) {
                    const rawPath = doc.file?.url || doc.url || '';
                    const normalizedUrl = rawPath.startsWith('http') ? rawPath : `${window.location.origin}/storage/${rawPath}`;

                    if (!existingDocUrls.has(normalizedUrl)) {
                        combinedDocuments.push(doc);
                        existingDocUrls.add(normalizedUrl);
                    }
                }
            } catch (e) {
                console.error(`Error loading customer documents for ID ${customer.customer_id}`, e);
            }
        }
    }

    // Prepare preselect list
    const preselectIds: number[] = [];

    for (const doc of combinedDocuments) {
        const rawPath = doc.file?.url || doc.url || '';
        const fullUrl = rawPath.startsWith('http') ? rawPath : `${window.location.origin}/storage/${rawPath}`;

        const fileName = doc.file?.name || rawPath.split('/').pop() || '';
        const extension = fileName.split('.').pop()?.toLowerCase();

        let type = 'Other';
        if (extension === 'pdf') type = 'PDF';
        else if (['jpg', 'jpeg'].includes(extension)) type = 'JPG';
        else if (extension === 'png') type = 'PNG';

        const fileEntry: FileData = {
            id: doc.id,
            name: fileName,
            url: fullUrl,
            typeId: doc.selection_type_id,
            type,
            size: doc.file?.size ?? 0,
            createdAt: new Date(doc.created_at).toLocaleString(),
            uploadedBy: doc.uploaded_by?.username || 'Unknown',
        };

        // Preselect if it's from original loan docs
        if (props.loan.status === 1 && loanDocs.some((ld) => ld.id === doc.id)) {
            preselectIds.push(doc.id);
        }

        // Push to "all"
        categorizedFiles.all.push(fileEntry);

        // Push to specific tab
        const tabKey = typeToTabKey[doc.selection_type_id];
        if (tabKey) {
            categorizedFiles[tabKey].push(fileEntry);
        }
    }

    // Set preselected documents only for status 1
    if (props.loan.status === 1) {
        selectedDocIds.value = preselectIds;
    }
});

const isImage = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png'].includes(ext ?? '');
};

function formatCurrency(value: number | string | null | undefined): string {
    if (value === null || value === undefined || isNaN(Number(value))) return '0.00';

    return Number(value).toLocaleString('en-MY', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    });
}

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

const submit = (newStatus: any) => {
    form.status = newStatus;
    form.activeTab = activeTab.value;
    const isSpecialSaveMode = form.bank.some((bank) => bank.save_bank === true);
    if (newStatus === 6) {
        submitWithConfirmation({
            form,
            confirmOptions: {
                showTextarea: true,
                textareaLabel: 'Reject Reason',
                textareaRequired: true,
            },
            submitOptions: {
                method: 'put',
                url: route('loans.update', props.loan.id),
                entityName: 'loan',
            },
        });
    } else {
        submitWithConfirmation({
            form,
            submitOptions: {
                method: 'put',
                url: route('loans.update', props.loan.id),
                entityName: 'loan',
            },
        });
    }
};

const goToTransactionTab = () => {
    router.visit(`${route('loan.transaction', props.loan.id)}?from=edit`);
};

const fetchCustomerCollaterals = async (collateral?: number) => {
    if (!collateral) return;

    try {
        const response = await axios.get(`/api/loans/collateral/${collateral}`);
        const collateralData = response.data;
        collateralData.customer_collateral_id = collateral;

        props.loan.loan_customer_collaterals.push(collateralData);
        const alreadyExists = form.collateral.some((item: any) => item.id === collateral);

        if (!alreadyExists) {
            form.collateral.push({ collateral_id: collateral, _delete: false });
        }
    } catch (error) {
        console.error('Error fetching collateral detail:', error);
    }
};

const removeCollateral = (index: number) => {
    const formItem = form.collateral[index];
    const removed = props.loan.loan_customer_collaterals.splice(index, 1)[0];

    if (formItem?.id && formItem.collateral_id === null) {
        form.collateral[index]._delete = true;
    } else if (!formItem?.id && formItem.collateral_id) {
        form.collateral = form.collateral.filter((_, i) => i !== index);
    }
};

const calculateAge = (birthDateStr: string | null): number | null => {
    if (!birthDateStr) return null;
    const birthDate = new Date(birthDateStr);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
};

watch(
    () => form.emergency.personal.birth_date,
    (newVal) => {
        form.emergency.personal.age = calculateAge(newVal);
    },
);

watch(
    () => form.guarantors.map((g: any) => g.personal.birth_date),
    (newDates) => {
        newDates.forEach((date: any, index: number) => {
            form.guarantors[index].personal.age = calculateAge(date);
        });
    },
    { deep: true },
);

watch(
    () => form.selection_type_id,
    (newVal, oldVal) => {
        const isNowCollateral = newVal === COLLATERAL_TYPE_ID;
        const wasCollateral = oldVal === COLLATERAL_TYPE_ID;

        if (!isNowCollateral && wasCollateral) {
            // User switched *away* from "Collateral" → clean up
            form.collateral = [];
        }
    },
);

watch(
    () => ({
        loanAmount: form.loan.loan_principle_amount,
        interestRate: form.loan.interest,
        totalInstallments: form.loan.no_of_instalment,
        lastPayment: form.loan.last_payment,
        status: form.status,
        rebate: form.loan.rebate,
    }),
    (values) => {
        const allFieldsFilled = Object.values(values.lastPayment).every((val) => val !== null && val !== '');

        if (!allFieldsFilled) {
            form.loan.instalment_amount = null;
            return;
        }

        const P = Number(values.loanAmount);
        const R = Number(values.interestRate) / 100;
        const N = Number(values.totalInstallments);
        const LP = Number(values.lastPayment);
        const Rebate = Number(values.rebate);

        let totalInterest = P * R * N;
        totalInterest = Rebate > 0 ? totalInterest - Rebate : totalInterest;

        const totalPayable = P + totalInterest;

        const remainingInstallments = N - 1;

        if (remainingInstallments <= 0) {
            form.loan.instalment_amount = Number((totalPayable - LP).toFixed(2));
            return;
        }

        const emi = (totalPayable - LP) / remainingInstallments;

        form.loan.instalment_amount = isFinite(emi) ? Number(emi.toFixed(2)) : null;
    },
);

const monthly = props.loanModes.find((type) => type.value === 'Monthly')?.id ?? null;
watch(
    () => ({
        commencementDate: form.loan.commencement_date,
        nextDueDate: form.loan.next_due_date,
        loanMode: form.loan.selection_mode_type_id,
    }),
    (values) => {
        if (values.loanMode === monthly && values.commencementDate) {
            try {
                const date = new Date(values.commencementDate);
                if (isNaN(date.getTime())) throw new Error('Invalid date');
                const originalDay = date.getDate(); // Store the original day
                // Add one month
                date.setMonth(date.getMonth() + 1);
                // Set back to the original day
                date.setDate(originalDay);
                // Check if the day was adjusted (e.g., Feb 30 → Feb 28/29)
                if (date.getDate() !== originalDay) {
                    // Adjust to the last day of the target month
                    date.setDate(0); // Moves to the last day of the previous month
                }
                // Subtract one day
                date.setDate(date.getDate() - 1);
                // Format as YYYY-MM-DD
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                form.loan.next_due_date = `${year}-${month}-${day}`;
            } catch (error) {
                form.loan.next_due_date = null; // Fallback if parsing fails
            }
        } else {
            // Reset next_due_date if loanMode is not monthly or commencementDate is empty
            form.loan.next_due_date = null;
        }
    },
    { immediate: true, deep: true },
);

watch(
    () => ({
        loanAmount: form.loan.loan_principle_amount,
        stampDuty: form.loan.stamp_duty,
        attestationFee: form.loan.attestation_fee,
        legalFee: form.loan.legal_fee,
        processingFee: form.loan.processing_fee,
        miscCharges: form.loan.misc_charges,
    }),
    (values) => {
        const allFieldsFilled = Object.values(values).every((val) => val !== null && val !== '' && !isNaN(parseFloat(val)));

        if (!allFieldsFilled) {
            form.loan.loan_disbursement_amount = null;
            return;
        }

        const loanAmount = Number(values.loanAmount);
        const stampDuty = Number(values.stampDuty);
        const attestationFee = Number(values.attestationFee);
        const legalFee = Number(values.legalFee);
        const processingFee = Number(values.processingFee);
        const miscCharges = Number(values.miscCharges);

        const loanDisbursementAmount = loanAmount - stampDuty - attestationFee - legalFee - processingFee - miscCharges;

        form.loan.loan_disbursement_amount = isFinite(loanDisbursementAmount) ? Number(loanDisbursementAmount.toFixed(2)) : null;
    },
    { immediate: true, deep: true },
);
</script>

<template>
    <AppLayout>
        <Head title="Edit Loan" />
        <div class="px-4 py-3">
            <Tabs default-value="loan-detail">
                <Heading title="Loans" pageNumber="P000012" description="Edit the selected loan record">
                    <template #status>
                        <Badge
                            :class="[
                                {
                                    'bg-ocean': props.loan.status === 1,
                                    'bg-canary': props.loan.status === 2,
                                    'bg-orange': props.loan.status === 3,
                                    'bg-chrome': props.loan.status === 4,
                                    'bg-castleton': props.loan.status === 5,
                                    'bg-pink': props.loan.status === 6,
                                    'bg-soften': props.loan.status === 7,
                                    'bg-cobalt': props.loan.status === 8,
                                    'bg-tomato': props.loan.status === 9,
                                    'bg-green': props.loan.status === 10,
                                    'bg-mist': props.loan.status === 11,
                                },
                                'text-md px-1 py-0',
                            ]"
                        >
                            {{ statusLabel(props.loan.status) }}
                        </Badge>
                    </template>
                    <template v-if="props.loan.status == 2" #slot>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.PENDING_PROCESS)"
                            type="button"
                            class="bg-teal flex items-center gap-2 text-white"
                        >
                            <FaIcon name="retweet" />
                            Revert
                        </Button>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.PENDING_APPROVAL)"
                            type="button"
                            class="bg-greenish flex items-center gap-2 text-white"
                        >
                            <FaIcon name="check" />
                            Approve
                        </Button>
                    </template>
                    <template v-if="props.loan.status == 3" #slot>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.PENDING_REVIEW)"
                            type="button"
                            class="bg-teal flex items-center gap-2 text-white"
                        >
                            <FaIcon name="retweet" />
                            Revert
                        </Button>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.REJECTED)"
                            type="button"
                            class="bg-destructive flex items-center gap-2 text-white"
                        >
                            <FaIcon name="x" />
                            Reject
                        </Button>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.APPROVED)"
                            type="button"
                            class="bg-greenish flex items-center gap-2 text-white"
                        >
                            <FaIcon name="check" />
                            Approve
                        </Button>
                    </template>
                    <template v-else-if="props.loan.status == 5" #slot>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.CUSTOMER_REJECTED)"
                            type="button"
                            class="bg-destructive flex items-center gap-2 text-white"
                        >
                            <FaIcon name="x" />
                            Customer Reject
                        </Button>
                        <Button
                            variant="outline"
                            @click="submit(loanStatus.CUSTOMER_ACCEPTED)"
                            type="button"
                            class="bg-greenish flex items-center gap-2 text-white"
                        >
                            <FaIcon name="check" />
                            Customer Accept
                        </Button>
                    </template>
                    <template v-if="props.loan.status >= 7" #slot>
                        <div class="border-gainsboro border">
                            <Button type="button" @click="" class="bg-azure hover:bg-azure px-4 py-2 text-white hover:text-white">
                                <FaIcon name="sack-dollar" class="pr-3" />
                                Loan Details
                            </Button>
                            <Button
                                type="button"
                                @click="goToTransactionTab"
                                class="bg-background hover:bg-background px-4 py-2 text-black hover:text-black"
                            >
                                <FaIcon name="right-left" class="pr-3" />
                                Transaction
                            </Button>
                        </div>
                    </template>
                </Heading>

                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Loan No.: {{ props.loan.code }}</CardTitle>
                    </CardHeader>
                    <TabsContent value="loan-detail">
                        <TabsWrapper v-model="activeTab" :tabs="tabItems">
                            <template #personal>
                                <CardContent class="py-4">
                                    <Label class="text-[20px]" for="">Personal Details</Label>
                                    <div v-if="props.loan.status == 1" class="grid gap-4 lg:grid-cols-2">
                                        <FormField
                                            v-for="field in personalFields"
                                            :id="field.id"
                                            :label="field.label"
                                            :model-value="field.modelValue"
                                            @update:model-value="field.updateValue"
                                            :type="field.type"
                                            :required="field.required"
                                            :placeholder="field.placeholder"
                                            :error="field.error"
                                            :options="field.options"
                                        />
                                    </div>
                                    <div v-else class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="company-name" class="text-base">Company Name</Label>
                                                <p>{{ props.loan.company }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="agent-name" class="text-base">Agent Name</Label>
                                                <p>{{ props.loan.agent }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="team-name" class="text-base">Team Name</Label>
                                                <p>{{ props.loan.team }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-type" class="text-base">Loan Type</Label>
                                                <p>{{ props.loan.type }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <FormShow :customer="props.loan.customer[0]" :ordinal="ordinal" />
                                    <Label class="text-[20px]" for="">Co-Borrower Info</Label>
                                    <FormShow
                                        v-for="(customer, index) in props.loan.customer.slice(1)"
                                        :label="`Co-Borrower ${index + 1}`"
                                        :customer="customer"
                                        :ordinal="ordinal"
                                    />
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #collateral>
                                <CardContent class="py-4">
                                    <template v-if="props.loan.status == 1">
                                        <Label class="text-[20px]">Collateral Info</Label>
                                        <p class="text-steel mb-2 text-sm italic">Search & Add New Collateral Details</p>
                                        <div class="grid gap-4 lg:grid-cols-2">
                                            <FormInputSearch
                                                api="/api/collaterals/search"
                                                :params="{ customer_ids: props.loan.customer.map((c: any) => c.customer_id) }"
                                                @resultsUpdated="handleCollateralResults"
                                                :labelKeywords="['remark', 'ownership_no', 'lot_number']"
                                                :icons="['gavel', 'house-user', 'clipboard-list']"
                                                iconPosition="right"
                                                :defaultSearchValue="'remark'"
                                            ></FormInputSearch>
                                        </div>
                                        <Collateral
                                            :form="props.loan.loan_customer_collaterals"
                                            :collateralTypes="props.collateralTypes"
                                            :ordinal="ordinal"
                                            isAccordion
                                            isShow
                                            isApplyLoan
                                            :removeCollateral="removeCollateral"
                                        />
                                    </template>
                                    <Collateral
                                        v-else
                                        :form="props.loan.loan_customer_collaterals"
                                        :collateralTypes="props.collateralTypes"
                                        :ordinal="ordinal"
                                        isAccordion
                                        isShow
                                    />
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #loan>
                                <CardContent class="py-4">
                                    <Label class="text-[20px]">Loan Details</Label>
                                    <div v-if="props.loan.status == 1" class="grid gap-4 lg:grid-cols-2">
                                        <FormField
                                            v-for="field in loanFields"
                                            :key="field.id"
                                            :id="field.id"
                                            :label="field.label"
                                            :model-value="field.modelValue"
                                            @update:model-value="field.updateValue"
                                            :type="field.type"
                                            :required="field.required"
                                            :placeholder="field.placeholder"
                                            :error="field.error"
                                            :options="field.options"
                                            :class="field.class"
                                            :selectPosition="field.selectPosition"
                                        />
                                    </div>
                                    <div v-else-if="props.loan.status == 5" class="grid grid-cols-2 gap-4 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Loan Mode
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ props.loan.loanDetail.mode_type }}
                                                </p>
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >No of Instalment
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ props.loan.loanDetail.no_of_instalment }}
                                                </p>
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Repayment Method
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ props.loan.loanDetail.repayment_method }}
                                                </p>
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Loan Commencement Date
                                                    <RequiredIndicator />
                                                </Label>
                                                <Calendar
                                                    v-model="form.loan.commencement_date"
                                                    placeholderLabel="Loan Commencement Date From"
                                                    :error="form.errors['loan.commencement_date']"
                                                    disableBeforeToday
                                                    disableMonthEndStart
                                                />
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Next Due Date
                                                    <RequiredIndicator />
                                                </Label>
                                                {{ form.loan.next_due_date ?? '-' }}
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Stamping Date
                                                    <RequiredIndicator />
                                                </Label>
                                                <Calendar
                                                    v-model="form.loan.stamping_date"
                                                    placeholderLabel="Stamping Date"
                                                    :error="form.errors['loan.stamping_date']"
                                                />
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Loan Principle Amount (RM)
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ formatCurrency(props.loan.loanDetail.loan_principle_amount) }}
                                                </p>
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="last-payment"
                                                    label="Last Payment (RM)"
                                                    :required="true"
                                                    v-model="form.loan.last_payment"
                                                    :error="form.errors['loan.last_payment']"
                                                    labelClass="text-base"
                                                    placeholder="Last Payment (RM)"
                                                />
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="rebate"
                                                    label="Rebate (RM)"
                                                    :required="false"
                                                    v-model="form.loan.rebate"
                                                    :error="form.errors['loan.rebate']"
                                                    labelClass="text-base"
                                                    placeholder="Rebate (RM)"
                                                />
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Instalment Amount (RM)
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ formatCurrency(form.loan.instalment_amount) }}
                                                </p>
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Interest
                                                    <RequiredIndicator />
                                                </Label>
                                                <p>
                                                    {{ props.loan.loanDetail.interest }}
                                                </p>
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="stamp-duty"
                                                    label="Stamp Duty (RM) (-)"
                                                    :required="true"
                                                    v-model="form.loan.stamp_duty"
                                                    :error="form.errors['loan.stamp_duty']"
                                                    labelClass="text-base"
                                                    placeholder="Stamp Duty (RM) (-)"
                                                />
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="attestation-fee"
                                                    label="Attestation Fee (RM) (-)"
                                                    :required="true"
                                                    v-model="form.loan.attestation_fee"
                                                    :error="form.errors['loan.attestation_fee']"
                                                    labelClass="text-base"
                                                    placeholder="Attestation Fee (RM) (-)"
                                                />
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="legal-fee"
                                                    label="Legal Fee (RM) (-)"
                                                    :required="true"
                                                    v-model="form.loan.legal_fee"
                                                    :error="form.errors['loan.legal_fee']"
                                                    labelClass="text-base"
                                                    placeholder="Legal Fee (RM) (-)"
                                                />
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="processing-fee"
                                                    label="Processing Fee (RM) (-)"
                                                    :required="true"
                                                    v-model="form.loan.processing_fee"
                                                    :error="form.errors['loan.processing_fee']"
                                                    labelClass="text-base"
                                                    placeholder="Processing Fee (RM) (-)"
                                                />
                                            </div>
                                            <div>
                                                <FormInputCurrency
                                                    id="misc-fee"
                                                    label="Misc Fee (RM) (-)"
                                                    :required="true"
                                                    v-model="form.loan.misc_charges"
                                                    :error="form.errors['loan.misc_charges']"
                                                    labelClass="text-base"
                                                    placeholder="Misc Fee (RM) (-)"
                                                />
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Late Payment Charges (%)
                                                    <RequiredIndicator />
                                                </Label>
                                                {{ props.loan.loanDetail.late_payment_charges }}
                                            </div>
                                            <div>
                                                <Label for="" class="text-base"
                                                    >Loan Disbursement Amount (RM)
                                                    <RequiredIndicator />
                                                </Label>
                                                {{ formatCurrency(form.loan.loan_disbursement_amount) ?? '0' }}
                                            </div>
                                        </div>
                                    </div>
                                    <div v-else class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-mode" class="text-base">Loan Mode</Label>
                                                <p>{{ props.loan.loanDetail.mode_type }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="no-instalment" class="text-base">No of Instalment</Label>
                                                <p>{{ props.loan.loanDetail.no_of_instalment }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="repayment-meyhod" class="text-base">Repayment Method</Label>
                                                <p>{{ props.loan.loanDetail.repayment_method }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="loan-commencement-date" class="text-base">Loan Commencement Date</Label>
                                                <p>{{ props.loan.loanDetail.commencement_date ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="next-due" class="text-base">Next Due Date</Label>
                                                <p>{{ props.loan.loanDetail.next_due_date ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="stamping-date" class="text-base">Stamping Date</Label>
                                                <p>{{ props.loan.loanDetail.stamping_date ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-principle-amount" class="text-base">Loan Principle Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.loan_principle_amount ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="last-payment" class="text-base">Last Payment (RM)</Label>
                                                <p>{{ props.loan.loanDetail.last_payment ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="rebate" class="text-base">Rebate (RM)</Label>
                                                <p>{{ props.loan.loanDetail.rebate ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="instalment-amount" class="text-base">Instalment Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.instalment_amount }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="interest" class="text-base">Interest (%)</Label>
                                                <p>{{ props.loan.loanDetail.interest }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="stamp-duty" class="text-base">Stamp Duty (RM) (-)</Label>
                                                <p>{{ props.loan.loanDetail.stamp_duty ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="attestation-fee" class="text-base">Attestation Fee (RM) (-)</Label>
                                                <p>{{ props.loan.loanDetail.attestation_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="legal-fee" class="text-base">Legal Fee (RM) (-)</Label>
                                                <p>{{ props.loan.loanDetail.legal_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="processing-fee" class="text-base">Processing Fee (RM) (-)</Label>
                                                <p>{{ props.loan.loanDetail.processing_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="misc-charges" class="text-base">Misc Charges (-)</Label>
                                                <p>{{ props.loan.loanDetail.misc_charges ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="late-payment-charges" class="text-base">Late Payment Charges (%)</Label>
                                                <p>{{ props.loan.loanDetail.late_payment_charges ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="loan-disbursement-amount" class="text-base">Loan Disbursement Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.loan_disbursement_amount ?? '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #emergency>
                                <CardContent class="py-4">
                                    <Label class="text-[20px]" for="">Emergency Contact</Label>
                                    <div v-if="props.loan.status == 1" class="grid gap-4 lg:grid-cols-2">
                                        <FormField
                                            v-for="field in emergencyFields"
                                            :key="field.id"
                                            :id="field.id"
                                            :label="field.label"
                                            :model-value="field.modelValue"
                                            @update:model-value="field.updateValue"
                                            :selectPlaceholder="field.selectPlaceholder"
                                            :type="field.type"
                                            :required="field.required"
                                            :placeholder="field.placeholder"
                                            :error="field.error"
                                            :options="field.options"
                                            :class="field.class"
                                            :selectPosition="field.selectPosition"
                                        />
                                    </div>
                                    <template v-else>
                                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                            <div class="space-y-3">
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="name" class="text-base">Name</Label>
                                                    <p>{{ props.loan.emergency.name ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="identity-no" class="text-base">Identity No.</Label>
                                                    <p>{{ props.loan.emergency.identity_no ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="birth-date" class="text-base">Date of Birth</Label>
                                                    <p>{{ props.loan.emergency.birth_date ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="age" class="text-base">Age</Label>
                                                    <p>{{ props.loan.emergency.age ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="gender" class="text-base">Gender</Label>
                                                    <p>{{ props.loan.emergency.gender_selection ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="relationship" class="text-base">Relationship</Label>
                                                    <p>{{ props.loan.emergency.relationship_selection ?? '-' }}</p>
                                                </div>
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="nationality" class="text-base">Nationality</Label>
                                                    <p>{{ props.loan.emergency.nationality_selection ?? '-' }}</p>
                                                </div>
                                            </div>
                                            <div class="space-y-3">
                                                <div class="flex items-start justify-between px-1">
                                                    <Label for="contact" class="text-base">Contact</Label>
                                                    <div class="flex flex-col text-right">
                                                        <div v-if="props.loan.emergency.personal_contact">
                                                            <div v-if="props.loan.emergency.personal_contact.telephone">
                                                                ({{ props.loan.emergency.personal_contact.telephone_country_selection ?? '' }})
                                                                {{ props.loan.emergency.personal_contact.telephone ?? '-' }}
                                                            </div>
                                                            <div v-if="props.loan.emergency.personal_contact.mobile_phone">
                                                                ({{ props.loan.emergency.personal_contact.mobile_country_selection ?? '' }})
                                                                {{ props.loan.emergency.personal_contact.mobile_phone ?? '-' }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <Separator class="my-4" />
                                        <div class="space-y-3">
                                            <div class="px-1">
                                                <Label for="address" class="py-0 text-base">Address </Label>
                                                <p class="whitespace-pre-wrap">
                                                    {{ formatAddress(props.loan.emergency.personal_address) }}
                                                </p>
                                            </div>
                                        </div>
                                    </template>
                                    <Separator class="my-4" />
                                    <Label class="text-[20px]" for="">Emergency Contact's Employment Info</Label>
                                    <div v-if="props.loan.status == 1" class="grid gap-4 lg:grid-cols-2">
                                        <FormField
                                            v-for="field in emergencyEmploymentFields"
                                            :key="field.id"
                                            :id="field.id"
                                            :label="field.label"
                                            :model-value="field.modelValue"
                                            @update:model-value="field.updateValue"
                                            :type="field.type"
                                            :required="field.required"
                                            :placeholder="field.placeholder"
                                            :error="field.error"
                                            :options="field.options"
                                            :class="field.class"
                                            :selectPosition="field.selectPosition"
                                        />
                                    </div>
                                    <template v-else>
                                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                            <div class="space-y-3">
                                                <div class="flex items-center justify-between px-1">
                                                    <Label for="employment-name" class="text-base">Name</Label>
                                                    <p>{{ props.loan.emergency.employment_name ?? '-' }}</p>
                                                </div>
                                            </div>
                                            <div class="space-y-3">
                                                <div class="flex items-start justify-between px-1">
                                                    <Label for="contact" class="text-base">Contact</Label>
                                                    <div class="flex flex-col text-right">
                                                        <div v-if="props.loan.emergency.employment_contact">
                                                            <div v-if="props.loan.emergency.employment_contact.telephone">
                                                                ({{ props.loan.emergency.employment_contact.telephone_country_selection ?? '' }})
                                                                {{ props.loan.emergency.employment_contact.telephone ?? '-' }}
                                                            </div>
                                                            <div v-if="props.loan.emergency.employment_contact.mobile_phone">
                                                                ({{ props.loan.emergency.employment_contact.mobile_country_selection ?? '' }})
                                                                {{ props.loan.emergency.employment_contact.mobile_phone ?? '-' }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <Separator class="my-4" />
                                        <div class="space-y-3">
                                            <div class="px-1">
                                                <Label for="address" class="py-0 text-base">Address</Label>
                                                <p class="whitespace-pre-wrap">
                                                    {{ formatAddress(props.loan.emergency.employment_address) }}
                                                </p>
                                            </div>
                                        </div>
                                        <Separator class="my-4" />
                                    </template>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #guarantor>
                                <CardContent class="py-4">
                                    <div class="flex justify-between pb-3">
                                        <Label class="text-[20px]">Guarantor Contact</Label>
                                        <Button
                                            v-if="props.loan.status == 1"
                                            type="button"
                                            class="bg-teal hover:bg-teal-hover flex items-center gap-2"
                                            @click="addGuarantor"
                                        >
                                            <FaIcon name="plus" />
                                            Add New
                                        </Button>
                                    </div>
                                    <Accordion type="single" class="w-full" collapsible>
                                        <AccordionItem
                                            v-for="(guarantor, index) in props.loan.status === 1 ? form.guarantors : props.loan.guarantors || []"
                                            :key="guarantor.id || index"
                                            :value="String(index)"
                                            class="mb-1"
                                        >
                                            <template v-if="!guarantor._delete">
                                                <Card v-if="props.loan.status == 1" class="gap-0 rounded-xs py-0">
                                                    <AccordionTrigger
                                                        class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline"
                                                    >
                                                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                            <FaIcon name="plus" />
                                                        </span>

                                                        <!-- Minus icon: visible when open -->
                                                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                            <FaIcon name="minus" />
                                                        </span>

                                                        <span class="flex-1 text-left font-medium"> {{ ordinal(index + 1) }} Guarantor </span>

                                                        <template #icon>
                                                            <Button
                                                                type="button"
                                                                @click.stop="removeGuarantor(index)"
                                                                variant="destructive"
                                                                class="flex items-center gap-1"
                                                            >
                                                                <FaIcon name="trash" />
                                                                Delete
                                                            </Button>
                                                        </template>
                                                    </AccordionTrigger>
                                                    <Separator />
                                                    <AccordionContent class="p-2">
                                                        <div class="grid gap-4 lg:grid-cols-2">
                                                            <FormField
                                                                v-for="field in getGuarantorFields(index)"
                                                                :key="field.id"
                                                                :id="field.id"
                                                                :label="field.label"
                                                                :model-value="field.modelValue"
                                                                @update:model-value="field.updateValue"
                                                                :type="field.type"
                                                                :required="field.required"
                                                                :selectPlaceholder="field.selectPlaceholder"
                                                                :placeholder="field.placeholder"
                                                                :error="field.error"
                                                                :options="field.options"
                                                                :class="field.class"
                                                                :selectPosition="field.selectPosition"
                                                            />
                                                        </div>
                                                        <Label class="pt-4 text-[20px]">Guarantor Contact's Employment info</Label>
                                                        <div class="grid gap-4 lg:grid-cols-2">
                                                            <FormField
                                                                v-for="field in getGuarantorEmploymentFields(index)"
                                                                :key="field.id"
                                                                :id="field.id"
                                                                :label="field.label"
                                                                :model-value="field.modelValue"
                                                                @update:model-value="field.updateValue"
                                                                :type="field.type"
                                                                :required="field.required"
                                                                :placeholder="field.placeholder"
                                                                :error="field.error"
                                                                :options="field.options"
                                                                :class="field.class"
                                                                :selectPosition="field.selectPosition"
                                                            />
                                                        </div>
                                                    </AccordionContent>
                                                </Card>
                                                <Card v-else class="gap-0 rounded-xs py-0">
                                                    <AccordionTrigger
                                                        class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline"
                                                    >
                                                        <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                            <FaIcon name="plus" />
                                                        </span>

                                                        <!-- Minus icon: visible when open -->
                                                        <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                            <FaIcon name="minus" />
                                                        </span>

                                                        <span class="flex-1 text-left font-medium"> {{ ordinal(index + 1) }} Guarantor </span>
                                                    </AccordionTrigger>
                                                    <Separator />
                                                    <AccordionContent class="p-2">
                                                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                                            <div class="space-y-3">
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Name</Label>
                                                                    <p>{{ guarantor.name ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Identity No.</Label>
                                                                    <p>{{ guarantor.identity_no ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Date of Birth</Label>
                                                                    <p>{{ guarantor.birth_date ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Age</Label>
                                                                    <p>{{ guarantor.age ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Gender</Label>
                                                                    <p>{{ guarantor.gender_selection ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Relationship</Label>
                                                                    <p>{{ guarantor.relationship_selection ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Nationality</Label>
                                                                    <p>{{ guarantor.nationality_selection ?? '-' }}</p>
                                                                </div>
                                                            </div>
                                                            <div class="space-y-3">
                                                                <div class="flex items-start justify-between px-1">
                                                                    <Label for="contact" class="text-base">Contact</Label>
                                                                    <div class="flex flex-col text-right">
                                                                        <div v-if="guarantor.personal_contact">
                                                                            <div v-if="guarantor.personal_contact.telephone">
                                                                                ({{ guarantor.personal_contact.telephone_country_selection ?? '' }})
                                                                                {{ guarantor.personal_contact.telephone ?? '-' }}
                                                                            </div>
                                                                            <div v-if="guarantor.personal_contact.mobile_phone">
                                                                                ({{ guarantor.personal_contact.mobile_country_selection ?? '' }})
                                                                                {{ guarantor.personal_contact.mobile_phone ?? '-' }}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <Separator class="my-4" />
                                                        <div class="space-y-3">
                                                            <div class="px-1">
                                                                <Label for="address" class="py-0 text-base">Address</Label>
                                                                <p class="whitespace-pre-wrap">
                                                                    {{ formatAddress(guarantor.personal_address) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <Separator class="my-4" />
                                                        <Label class="text-[20px]" for="">Guarantor Contact's Employment Info</Label>
                                                        <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                                            <div class="space-y-3">
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Terms of Employment</Label>
                                                                    <p>{{ guarantor.terms_of_employment_selection ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Employment Name</Label>
                                                                    <p>{{ guarantor.employment_name ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Job Position</Label>
                                                                    <p>{{ guarantor.job_position ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Length Service Year</Label>
                                                                    <p>{{ guarantor.length_service_year ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Length Service Month</Label>
                                                                    <p>{{ guarantor.length_service_month ?? '-' }}</p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Business Classification</Label>
                                                                    <p>
                                                                        {{ guarantor.business_classification_selection ?? '-' }}
                                                                    </p>
                                                                </div>
                                                                <div class="flex items-center justify-between px-1">
                                                                    <Label for="" class="text-base">Occupation</Label>
                                                                    <p>{{ guarantor.occupation_selection ?? '-' }}</p>
                                                                </div>
                                                            </div>
                                                            <div class="space-y-3">
                                                                <div class="flex items-start justify-between px-1">
                                                                    <Label for="contact" class="text-base">Contact</Label>
                                                                    <div class="flex flex-col text-right">
                                                                        <div v-if="guarantor.employment_contact">
                                                                            <div v-if="guarantor.employment_contact.telephone">
                                                                                ({{ guarantor.employment_contact.telephone_country_selection ?? '' }})
                                                                                {{ guarantor.employment_contact.telephone ?? '-' }}
                                                                            </div>
                                                                            <div v-if="guarantor.employment_contact.mobile_phone">
                                                                                ({{ guarantor.employment_contact.mobile_country_selection ?? '' }})
                                                                                {{ guarantor.employment_contact.mobile_phone ?? '-' }}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <Separator class="my-4" />
                                                        <div class="space-y-3">
                                                            <div class="px-1">
                                                                <Label for="address" class="py-0 text-base">Address</Label>
                                                                <p class="whitespace-pre-wrap">
                                                                    {{ formatAddress(guarantor.employment_address) }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </AccordionContent>
                                                </Card>
                                            </template>
                                        </AccordionItem>
                                    </Accordion>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #bank>
                                <CardContent class="py-4">
                                    <div class="mb-2 flex items-center justify-between pt-3">
                                        <Label class="text-[20px]" for="">Disbursement Account</Label>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            class="bg-teal hover:bg-teal-hover text-white hover:text-white"
                                            @click="addBank"
                                        >
                                            <FaIcon name="plus" />
                                            Add Bank
                                        </Button>
                                    </div>
                                    <Table>
                                        <TableHeader>
                                            <TableRow class="bg-white">
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Account Type
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Name
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Holder Name
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Account Number
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Action
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow v-for="(bank, index) in form.bank" :key="index" class="border">
                                                <TableCell v-if="!bank._delete" class="border-sort border px-3 py-1.5">
                                                    <Select v-model="bank.selection_type_id">
                                                        <SelectTrigger class="w-full">
                                                            <SelectValue placeholder="Select Bank Account Type" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem v-for="type in bankAccountTypes" :key="type.id" :value="type.id">
                                                                {{ type.value }}
                                                            </SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </TableCell>
                                                <TableCell v-if="!bank._delete" class="border-sort border px-3 py-1.5">
                                                    <Select v-model="bank.selection_bank_id">
                                                        <SelectTrigger class="w-full">
                                                            <SelectValue placeholder="Select Bank" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            <SelectItem v-for="bank in bankTypes" :key="bank.id" :value="bank.id">
                                                                {{ bank.value }}
                                                            </SelectItem>
                                                        </SelectContent>
                                                    </Select>
                                                </TableCell>
                                                <TableCell v-if="!bank._delete" class="border-sort border px-3 py-1.5">
                                                    <Input
                                                        id="account-name"
                                                        v-model="bank.account_name"
                                                        :error="form.errors['bank.account_name']"
                                                        required
                                                        placeholder="Bank Holder Name"
                                                    />
                                                </TableCell>
                                                <TableCell v-if="!bank._delete" class="border-sort border px-3 py-1.5">
                                                    <Input
                                                        id="account-no"
                                                        v-model="bank.account_no"
                                                        :error="form.errors['bank.account_no']"
                                                        required
                                                        placeholder="Bank Account Number"
                                                    />
                                                </TableCell>
                                                <TableCell v-if="!bank._delete" class="border-sort border px-3 py-1.5 text-center">
                                                    <Button variant="ghost" size="sm" @click="removeBank(index)">
                                                        <FaIcon name="trash" class="text-destructive" />
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                    <div class="mb-2 flex items-center justify-end pt-3">
                                        <Button
                                            variant="outline"
                                            @click="submit(props.loan.status)"
                                            type="button"
                                            class="bg-green flex items-center justify-end gap-2 text-white"
                                        >
                                            Save
                                            <FaIcon name="paper-plane" />
                                        </Button>
                                    </div>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>
                            <template #document>
                                <CardContent class="py-4">
                                    <div class="flex gap-4">
                                        <div class="flex w-[75%] flex-col gap-2">
                                            <Card class="h-[420px] w-full rounded-lg py-2">
                                                <CardContent class="px-2">
                                                    <TabsWrapper v-model="activeDocTab" :tabs="docItems">
                                                        <template #all>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['all'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['all']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #customer-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['customer-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['customer-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #collateral-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['collateral-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['collateral-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #security-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['security-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['security-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>
                                                    </TabsWrapper>
                                                </CardContent>
                                            </Card>
                                        </div>
                                        <div class="w-[25%]">
                                            <Card class="flex h-[500px]" v-if="selectedDocIds.length > 0">
                                                <CardContent>
                                                    <template v-if="selectedDocIds.length === 1 && selectedFile">
                                                        <div class="mt-4 w-full max-w-md">
                                                            <a
                                                                :href="selectedFile.url"
                                                                target="_blank"
                                                                class="mb-3 block truncate text-sm text-blue-700 hover:underline"
                                                            >
                                                                {{ selectedFile.name }}
                                                            </a>

                                                            <!-- Icon -->
                                                            <div v-if="isImage(selectedFile.name)" class="mb-6 text-center">
                                                                <img
                                                                    :src="selectedFile.url"
                                                                    alt="preview"
                                                                    class="h-auto max-h-[150px] w-full object-contain"
                                                                />
                                                            </div>
                                                            <div v-else class="mb-6 text-center">
                                                                <span class="text-lavender text-[50px]">
                                                                    <FaIcon name="file" />
                                                                </span>
                                                            </div>

                                                            <!-- Details -->
                                                            <div class="space-y-4 text-sm">
                                                                <div>
                                                                    <p class="font-semibold">File Type:</p>
                                                                    <p>{{ selectedFile.type }}</p>
                                                                </div>
                                                                <div>
                                                                    <p class="font-semibold">File Size:</p>
                                                                    <p>{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
                                                                </div>
                                                                <div>
                                                                    <p class="font-semibold">Created At:</p>
                                                                    <p>{{ selectedFile.createdAt }}</p>
                                                                </div>
                                                                <div>
                                                                    <p class="font-semibold">Uploaded By:</p>
                                                                    <p>{{ selectedFile.uploadedBy }}</p>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </template>
                                                    <template v-else>
                                                        <div class="text-center">
                                                            <span class="text-lavender text-[50px]">
                                                                <FaIcon name="file" />
                                                            </span>
                                                        </div>
                                                        <p class="text-center">{{ selectedDocIds.length }} items selected.</p>
                                                    </template>
                                                </CardContent>
                                            </Card>
                                            <Card v-else class="flex h-[500px] items-center justify-center">
                                                <CardContent>
                                                    <div class="text-center">
                                                        <span class="text-lavender text-[50px]">
                                                            <FaIcon name="file" />
                                                        </span>
                                                        <p>0 Item</p>
                                                        <p>Select a single file to get more information</p>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" />
                                        Back
                                    </Button>
                                    <template v-if="props.loan.status == 1">
                                        <Button
                                            type="button"
                                            @click="submit(loanStatus.PENDING_PROCESS)"
                                            class="bg-teal flex items-center gap-2 text-white"
                                        >
                                            <FaIcon name="floppy-disk" />
                                            Save as Draft
                                        </Button>
                                        <Button
                                            type="button"
                                            @click="submit(loanStatus.PENDING_REVIEW)"
                                            class="bg-green flex items-center gap-2 text-white"
                                        >
                                            Submit
                                            <FaIcon name="paper-plane" />
                                        </Button>
                                    </template>
                                </CardFooter>
                            </template>
                        </TabsWrapper>
                    </TabsContent>
                    <TabsContent value="transaction"></TabsContent>
                </Card>
            </Tabs>
        </div>
    </AppLayout>
</template>
